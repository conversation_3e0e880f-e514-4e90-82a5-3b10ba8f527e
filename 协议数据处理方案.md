# 上海联劝公益基金会协议数据迁移处理方案

## 一、项目背景
为配合Boss3新业务系统上线，需要对历史协议数据进行清理和标准化处理，确保数据质量和系统兼容性。

## 二、数据现状分析

### 2.1 数据文件概况
- **合同类型子表**：21条标准映射记录，定义了合同类别与协议类别的对应关系
- **待确认协议数据**：286条协议记录，其中47条已处理，239条待处理

### 2.2 主要数据问题
1. **Boss3对应合同类型**字段存在错误
   - 部分记录的合同类型与实际业务不符
   - 个别记录该字段为空白
2. **Boss3对应协议类别**字段需要标准化
   - 需要根据合同类型子表进行统一规范
   - 确保与Boss3系统字典一致

## 三、处理流程设计

### 3.1 数据验证阶段（第1-2周）
**目标**：识别并标记所有数据异常

**步骤**：
1. 逐条核对"Boss3对应合同类型"与"原合同类型"的一致性
2. 标记不一致或空白的记录
3. 根据合同名称和业务性质判断正确的合同类型

**输出**：《数据异常清单》

### 3.2 数据修正阶段（第3-4周）
**目标**：修正错误数据，统一协议类别

**步骤**：
1. 修正"Boss3对应合同类型"字段错误
2. 根据合同类型子表映射，填写标准的"Boss3对应协议类别"
3. 业务团队复核确认

**输出**：《修正后数据文件》

### 3.3 质量检查阶段（第5周）
**目标**：确保数据质量达到迁移标准

**步骤**：
1. 全量数据一致性检查
2. 抽样业务逻辑验证
3. 系统兼容性测试

**输出**：《数据质量报告》

## 四、操作指南

### 4.1 合同类型判断标准
参考合同类型子表，按以下原则判断：

**收款类型**：
- 捐赠协议、服务协议（收款）、补充协议（收款）、组队协议

**付款类型**：
- 资助协议、服务协议（付款）、补充协议（付款）
- 审计业务约定书、采购合同、租赁协议

**合作类型**：
- 专项基金设立协议、合作协议、终止协议、授权协议
- 服务协议（合作）、补充协议（合作）、重大事项报备、合作备忘录

**多方协议类型**：
- 定向捐赠协议、补充协议（多方协议）

### 4.2 数据修正操作步骤
1. 打开"待确认协议数据.csv"文件
2. 筛选"是否已经处理"字段为空白的记录
3. 逐条检查"Boss3对应合同类型"字段：
   - 与"原合同类型"对比，如不一致需要修正
   - 参考合同名称判断正确类型
4. 根据确定的合同类型，在合同类型子表中查找对应的协议类别
5. 填写"Boss3对应协议类别"字段
6. 标记"是否已经处理"为"已处理"

### 4.3 质量控制要点
- 每处理50条记录进行一次自检
- 疑难问题及时向项目负责人咨询
- 保持处理记录的可追溯性

## 五、时间安排

| 阶段 | 时间 | 负责人 | 交付物 |
|------|------|--------|--------|
| 数据验证 | 第1-2周 | 业务团队 | 数据异常清单 |
| 数据修正 | 第3-4周 | 业务团队 | 修正后数据文件 |
| 质量检查 | 第5周 | 项目组 | 数据质量报告 |
| 系统迁移 | 第6周 | 技术团队 | 迁移完成确认 |

## 六、风险控制

### 6.1 数据风险
- **风险**：数据修正过程中出现误操作
- **控制措施**：每日备份，建立版本管理

### 6.2 进度风险
- **风险**：处理进度滞后影响系统上线
- **控制措施**：每周进度检查，及时调整资源

### 6.3 质量风险
- **风险**：数据质量不达标影响业务运行
- **控制措施**：多轮验证，抽样复核

## 七、支持资源

### 7.1 技术支持
- 数据处理工具培训
- 系统操作指导

### 7.2 业务支持
- 疑难问题解答
- 业务逻辑确认

### 7.3 项目管理
- 进度跟踪
- 问题协调

## 八、成功标准
1. 数据完整性：100%记录处理完成
2. 数据准确性：错误率<1%
3. 系统兼容性：通过Boss3系统验证
4. 时间达成：按计划完成迁移

---
*本方案由数字化转型项目组制定，如有疑问请联系项目负责人。*
