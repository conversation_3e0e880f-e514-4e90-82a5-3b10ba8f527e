# 协议数据处理检查清单

## 处理前准备工作

### ✅ 文件准备
- [ ] 已下载"待迁移数据_待确认协议数据.csv"
- [ ] 已下载"待迁移数据_合同类型子表_表格.csv"  
- [ ] 已创建工作文件夹并备份原始数据
- [ ] 已了解本组负责处理的协议范围

### ✅ 工具准备
- [ ] Excel或WPS表格软件正常运行
- [ ] 已熟悉筛选、查找功能的使用
- [ ] 已准备处理记录表格

## 逐条处理检查清单

### 对于每一条协议记录，请确认：

#### 1. 基础信息核查
- [ ] 合同编号无误
- [ ] 合同名称完整
- [ ] 合作方信息准确
- [ ] 项目名称正确

#### 2. 合同类型验证
- [ ] "原合同类型"与"Boss3对应合同类型"是否一致
- [ ] 如不一致，已根据业务性质判断正确类型
- [ ] 已参考合同名称关键词进行验证
- [ ] 已考虑资金流向（收款/付款/合作）

#### 3. 协议类别映射
- [ ] 已在合同类型子表中查找对应的协议类别
- [ ] "Boss3对应协议类别"字段填写完整
- [ ] 协议类别名称与子表完全一致
- [ ] 已确认合同类型与协议类别的匹配关系

#### 4. 完成标记
- [ ] "是否已经处理"字段标记为"已处理"
- [ ] "录入人"字段确认无误
- [ ] 如有特殊情况已在备注中说明

## 常见错误检查

### ❌ 避免以下错误：
- [ ] 合同类型与实际业务不符
- [ ] 协议类别名称拼写错误
- [ ] 协议类别与合同类型不匹配
- [ ] 遗漏必填字段
- [ ] 处理标记未更新

### ✅ 质量自检：
- [ ] 每处理10条记录进行一次自检
- [ ] 同类协议处理标准保持一致
- [ ] 疑难问题已记录并咨询
- [ ] 处理逻辑符合业务实际

## 特殊情况处理

### 情况1：合同类型无法确定
**处理方式：**
- [ ] 已查看合同名称关键词
- [ ] 已参考协议原对应业务类型
- [ ] 已咨询相关业务人员
- [ ] 如仍无法确定，已标记"待确认"并提交项目组

### 情况2：协议类别在子表中无对应
**处理方式：**
- [ ] 已选择最接近的类别
- [ ] 已在备注中说明具体情况
- [ ] 已提交项目组讨论

### 情况3：历史协议信息不完整
**处理方式：**
- [ ] 已根据现有信息尽力判断
- [ ] 已标记信息缺失部分
- [ ] 已咨询相关历史经办人

## 每日工作总结

### 处理进度记录
- 处理日期：_______
- 处理数量：_______条
- 累计完成：_______条
- 剩余数量：_______条

### 问题记录
- 疑难协议编号：_______
- 问题描述：_______
- 处理方式：_______
- 是否需要升级：_______

### 质量检查
- 自检发现错误：_______条
- 已修正错误：_______条
- 质量达标率：_______%

## 提交前最终检查

### ✅ 数据完整性
- [ ] 所有负责的协议均已处理
- [ ] 必填字段无遗漏
- [ ] 处理标记已更新

### ✅ 数据准确性  
- [ ] 合同类型判断准确
- [ ] 协议类别映射正确
- [ ] 特殊情况已标注

### ✅ 格式规范性
- [ ] 字段内容格式统一
- [ ] 无多余空格或特殊字符
- [ ] 文件保存格式正确

### ✅ 备份与提交
- [ ] 已备份处理后的文件
- [ ] 已按要求命名文件
- [ ] 已准备提交材料

---

**处理人签名：**_____________  
**处理日期：**_____________  
**复核人签名：**_____________  
**复核日期：**_____________

---

*本检查清单请在处理过程中逐项确认，确保数据质量达到迁移标准。*
